using System.Data.Common;
using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using Npgsql;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Extensions;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;
using Q.FilterBuilder.IntegrationTests.Database.Models;

namespace Q.FilterBuilder.IntegrationTests.Services;

/// <summary>
/// Provider-specific ORM execution service using new core extension methods
/// </summary>
public class OrmExecutionService : IOrmExecutionService
{
    private readonly TestDbContext _context;
    private readonly TestConfiguration _testConfig;
    private readonly string _connectionString;
    private readonly IFilterBuilder _filterBuilder;

    public OrmExecutionService(TestDbContext context, TestConfiguration testConfig, IFilterBuilder filterBuilder)
    {
        _context = context;
        _testConfig = testConfig;
        _connectionString = _context.Database.GetConnectionString()!;
        _filterBuilder = filterBuilder;
    }

    public async Task<List<User>> ExecuteWithEntityFrameworkAsync(string formattedQuery, object[] parameters)
    {
        var tableName = GetTableName("Users");
        var sql = $"SELECT * FROM {tableName} WHERE {formattedQuery}";
        return await _context.Set<User>().FromSqlRaw(sql, parameters).ToListAsync();
    }

    public async Task<List<dynamic>> ExecuteWithDapperAsync(string whereClause, Dictionary<string, object?> parameters)
    {
        var tableName = GetTableName("Users");
        var sql = $"SELECT * FROM {tableName} WHERE {whereClause}";

        // Execute with the appropriate connection type
        var provider = _testConfig.GetDatabaseProvider();
        return provider switch
        {
            DatabaseProvider.SqlServer => await ExecuteDapperAsync(new SqlConnection(_connectionString), sql, parameters),
            DatabaseProvider.MySql => await ExecuteDapperAsync(new MySqlConnection(_connectionString), sql, parameters),
            DatabaseProvider.PostgreSql => await ExecuteDapperAsync(new NpgsqlConnection(_connectionString), sql, parameters),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }

    public async Task<List<dynamic>> ExecuteWithAdoNetAsync(string whereClause, object[] parameters)
    {
        var tableName = GetTableName("Users");
        var sql = $"SELECT * FROM {tableName} WHERE {whereClause}";

        // Execute with the appropriate connection type
        var provider = _testConfig.GetDatabaseProvider();
        return provider switch
        {
            DatabaseProvider.SqlServer => await ExecuteAdoNetAsync(new SqlConnection(_connectionString), sql, parameters),
            DatabaseProvider.MySql => await ExecuteAdoNetAsync(new MySqlConnection(_connectionString), sql, parameters),
            DatabaseProvider.PostgreSql => await ExecuteAdoNetAsync(new NpgsqlConnection(_connectionString), sql, parameters),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }

    private string GetTableName(string tableName)
    {
        // Use the FilterBuilder's QueryFormatProvider to format table names consistently
        return _filterBuilder.QueryFormatProvider.FormatFieldName(tableName);
    }

    /// <summary>
    /// Generic Dapper execution method that works with any connection type
    /// </summary>
    private async Task<List<dynamic>> ExecuteDapperAsync(DbConnection connection, string sql, Dictionary<string, object?> parameters)
    {
        var results = await connection.QueryAsync(sql, parameters);
        return results.ToList();
    }

    /// <summary>
    /// Generic ADO.NET execution method that works with any connection type
    /// </summary>
    private async Task<List<dynamic>> ExecuteAdoNetAsync(DbConnection connection, string sql, object[] parameters)
    {
        using var command = connection.CreateCommand();
        command.CommandText = sql;

        // Use the FilterBuilder's QueryFormatProvider to create parameters consistently
        //for (var i = 0; i < parameters.Length; i++)
        //{
        //    var parameter = command.CreateParameter();
        //    parameter.ParameterName = _filterBuilder.QueryFormatProvider.FormatParameterName(i);
        //    parameter.Value = parameters[i] ?? DBNull.Value;
        //    command.Parameters.Add(parameter);
        //}

        command.AddParameters(parameters, _filterBuilder.QueryFormatProvider);

        await connection.OpenAsync();
        using var reader = await command.ExecuteReaderAsync();
        return await ReadResultsAsync(reader);
    }



    private static async Task<List<dynamic>> ReadResultsAsync(DbDataReader reader)
    {
        var results = new List<dynamic>();
        while (await reader.ReadAsync())
        {
            var row = new Dictionary<string, object?>();
            for (var i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
            }
            results.Add(row);
        }
        return results;
    }
}
