{"Version": 1, "WorkspaceRootPath": "C:\\git-repos\\DynamicWhereBuilder\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\controllers\\integrationtestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\controllers\\integrationtestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\complexrulestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\complexrulestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\appsettings.test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\appsettings.test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\dapperintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\dapperintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\efcoreintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\efcoreintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\datatypetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\datatypetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\tests\\adonetintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\tests\\adonetintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\services\\ormexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-567890123456}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\services\\ormexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.core\\extensions\\adonetextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|solutionrelative:src\\q.filterbuilder.core\\extensions\\adonetextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 592, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "DapperIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\DapperIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\DapperIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\DapperIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\DapperIntegrationTests.cs", "ViewState": "AgIAAF0AAAAAAAAAAAAmwG4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T10:04:22.496Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DataTypeTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\DataTypeTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\DataTypeTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\DataTypeTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\DataTypeTests.cs", "ViewState": "AgIAAKIAAAAAAAAAAAAuwLYAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:59:01.404Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ComplexRulesTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexRulesTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexRulesTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexRulesTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\ComplexRulesTests.cs", "ViewState": "AgIAAFUAAAAAAAAAAAAmwGIAAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:51:30.629Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AdoNetIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\AdoNetIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\AdoNetIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\AdoNetIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\AdoNetIntegrationTests.cs", "ViewState": "AgIAAA0AAAAAAAAAAIA0wBEAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:35:48.877Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.test.json", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-16T09:16:53.789Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "EFCoreIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\EFCoreIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\EFCoreIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Tests\\EFCoreIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Tests\\EFCoreIntegrationTests.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAmwBEAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:47:50.958Z", "EditorCaption": ""}]}, {"DockedWidth": 570, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 8, "Title": "AdoNetExtensions.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "RelativeToolTip": "src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "ViewState": "AgIAABQAAAAAAAAAAAAQwA4AAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:41:05.783Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "OrmExecutionService.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "ViewState": "AgIAACcAAAAAAAAAAAAuwD4AAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:39:19.719Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "IntegrationTestController.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Controllers\\IntegrationTestController.cs", "ViewState": "AgIAAHAAAAAAAAAAAAAuwIUAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:35:56.576Z", "EditorCaption": ""}]}]}]}